<template>
  <el-dialog
    title="订单详情"
    v-model="open"
    width="880px"
    append-to-body
    :close-on-click-modal="false"
    @close="cancel"
  >
    <el-form ref="tagRef" :model="form" label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="状态">
            <dict-tag :options="order_status" :value="form?.status"></dict-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单号">
            {{ form?.orderNo || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单类型">
            <dict-tag :options="order_type" :value="form?.orderType"></dict-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户手机号">
            {{ form?.phone || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="车牌号">
            {{ form?.plateNo || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="车场">
            {{ form?.parkName || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="车位信息">
            <el-collapse style="width: 300px">
              <el-collapse-item title="查看详情" name="1">
                <el-card style="width: 300px">
                  <el-row>
                    <el-col :span="6"> 设备编号: </el-col>
                    <el-col :span="18">
                      {{ billInfo?.lockNumbers || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 区域: </el-col>
                    <el-col :span="18"> {{ billInfo?.location || '-' }}</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 车位号: </el-col>
                    <el-col :span="18"> {{ billInfo?.code || '-' }} </el-col>
                  </el-row>
                </el-card>
              </el-collapse-item>
            </el-collapse>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="下单时间">
            {{ form?.orderTime ? parseTime(form.orderTime) : '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间">
            {{ form?.finishTime ? parseTime(form.finishTime) : '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="收费标准">
            <el-collapse style="width: 300px">
              <el-collapse-item title="查看详情" name="1">
                <el-card style="width: 300px">
                  <el-row>
                    <el-col :span="6"> 锁单价: </el-col>
                    <el-col :span="18">
                      {{ billInfo?.price || '-' }} 元每分钟
                    </el-col> 
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 封顶价格: </el-col>
                    <el-col :span="18"> {{ billInfo?.capAmount || '-' }}元</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 共享时间: </el-col>
                    <el-col :span="18">
                      {{ billInfo?.shareStartTime ? billInfo?.shareStartTime : '-' }} ~
                      {{ billInfo?.shareEndTime ? billInfo?.shareEndTime : '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 车场单价: </el-col>
                    <el-col :span="18"> {{ billInfo?.price || '-' }} </el-col>
                  </el-row>
                </el-card>
              </el-collapse-item>
            </el-collapse>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="正常消费">
            {{ form?.parkAmount || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="优惠折扣">
            <el-collapse style="width: 300px">
              <el-collapse-item title="查看详情" name="1">
                <el-card style="width: 300px">
                  <el-row>
                    <el-col :span="6"> 折扣: </el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.discount || '-' }}折
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 正常价格: </el-col>
                    <el-col :span="18"> {{ form?.preferentialAmount || '-' }}</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 免费时长: </el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.freeTime || '-' }}小时
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 锁单价: </el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.price || '-' }}元/分钟*60
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 免费时间: </el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.startTime || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 订单时间: </el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.price || '-' }}元/分钟*60
                    </el-col>
                  </el-row>
                </el-card>
              </el-collapse-item>
            </el-collapse>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="额外费用">
            <el-collapse style="width: 300px">
              <el-collapse-item :title="(form?.timeoutAmount || 0) + '元'" name="1">
                <el-card style="width: 300px">
                  <el-row>
                    <el-col :span="6"> 入场滞留费: </el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.discount || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 进场时间: </el-col>
                    <el-col :span="18"> {{ orderPriceList?.location || '-' }}</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 关锁时间: </el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.freeTime || '-' }}小时
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 滞留时间:</el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.price || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 费用计算:</el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.freeTime || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 出场滞留费:</el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.code || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 开锁时间:</el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.code || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 出场时间:</el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.code || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 滞留时间 :</el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.code || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 费用计算: </el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.code || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 超时停放:</el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.code || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 共享时间:</el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.code || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 开锁时间:</el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.code || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 超时停放:</el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.code || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6"> 费用计算:</el-col>
                    <el-col :span="18">
                      {{ orderPriceList?.code || '-' }}
                    </el-col>
                  </el-row>
                </el-card>
              </el-collapse-item>
            </el-collapse>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应收金额">
            {{ form?.payableAmount || 0 }}元
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实收金额">
            {{ form?.realPayAmount || 0 }}元
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="退费金额">
            {{ form?.refundPendingAmount || 0 }}元
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="待补缴金额">
            {{ form?.unpaidAmount || 0 }}元
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="人工退费">
            <el-collapse style="width: 300px" v-if="form?.manualRefundAmount">
              <el-collapse-item :title="(form?.manualRefundAmount || 0) + '元'" name="1">
                <el-card style="width: 300px">
                  <el-row>
                    <el-col :span="7"> 操作人员： </el-col>
                    <el-col :span="17">
                      {{ refundInfo?.operator || '-' }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="7"> 操作时间： </el-col>
                    <el-col :span="17"> {{ refundInfo?.refundTime || '-' }}</el-col>
                  </el-row>
                </el-card>
              </el-collapse-item>
            </el-collapse>
            <div v-else>无</div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="分成扣除"> 暂无 </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际收入">
            {{ form?.realPayAmount || 0 }} 元
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="refund" type="primary" v-if="type == 1"
          >退 款</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog title="退款" v-model="refundOpen" width="580px" append-to-body>
    <el-form
      ref="tagRef"
      :model="refundForm"
      :rules="refundRules"
      label-width="80px"
    >
      <div style="margin-bottom: 20px">
        <el-tag type="danger">
          确定是否进行退款，退款后无法恢复(可退金额不可大于消费金额/保证金)。</el-tag
        >
      </div>
      <el-row>
        <el-col :span="24">
          <el-form-item label="退款金额" prop="refundFee">
            <el-input
              v-model="refundForm.refundFee"
              placeholder="请输入退款金额"
              type="number"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="交易密码" prop="password">
            <el-input
              v-model="refundForm.password"
              placeholder="请输入交易密码"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="refundCancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { watch } from "vue";
import {
  listOrder,
  getOrder,
  getOrderPrice,
  getRefundInfo,
  getBillInfo,
  getBreachPrice,
  listLockOrder,
} from "@/api/deveice/order";
import { applyRefund } from "@/api/finance/statements";
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "订单详情",
  },
  item: {
    type: Object,
    default: () => ({}),
  },
  type: {
    type: String,
    default: "1",
  },
});
const emit = defineEmits(["update:modelValue"]);
const { proxy } = getCurrentInstance();
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      getDetails(props.item);
    }
  }
);
//  优惠价格详情
const orderPriceList = ref({});
// 退款详情
const refundInfo = ref({});
// 收费标准
const billInfo = ref({});
// 违约金
const breachPrice = ref({});
const open = ref(false);
const form = ref({});
// 退款
const refundOpen = ref(false);
const refundForm = ref({});
const refundRules = {
  refundFee: [
    { required: true, message: "退款金额不能为空", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value > form.value.actualFee) {
          callback(new Error("退款金额不能大于应收金额"));
        } else {
          callback();
        }
      },
    },
  ],
  password: [{ required: true, message: "交易密码不能为空", trigger: "blur" }],
};

const getDetails = (row) => {
  console.log(row);

  getOrder(row.orderId).then((response) => {
    form.value = response.data;
    open.value = true;

    //查询当前下单用户的优惠价格详情
    getOrderPriceList({
      parkId: response.data.parkId,
      phone: response.data.phone,
      lotShareRuleId: response.data.lotShareRuleId,
    });
    //查询退款详情
    getRefundInfoList({
      orderId: row.orderId,
    });
    // 通过billId查询车位信息以及收费标准
    getBillInfoList(response.data.lotShareRuleId);
    //查询当前下单用户的违约价格详情
    // getBreachPriceList({
    //   orderId: row.orderId,

    //   lotShareRuleId: response.data.lotShareRuleId,
    // });
  });
};
// 查询当前下单用户的优惠价格详情
function getOrderPriceList(query) {
  getOrderPrice(query).then((response) => {
    orderPriceList.value = response.data;
  });
}
// 获取用户订单中需要退款的信息
function getRefundInfoList(query) {
  getRefundInfo(query).then((response) => {
    refundInfo.value = response.data;
  });
}
// 通过billId查询车位信息以及收费标准
function getBillInfoList(query) {
  getBillInfo(query).then((response) => {
    billInfo.value = response.data;
  });
}
//查询当前下单用户的违约价格详情
// function getBreachPriceList(query) {
//   getBreachPrice(query).then((response) => {
//     breachPrice.value = response.data;
//   });
// }

// 静态数据
const order_type = [
  { label: "普通计费", value: "1" },
  { label: "特殊计费", value: "2" },
];

const order_status = [
  { label: "进行中", value: "0" },
  { label: "已部分支付(待补缴)", value: "1" },
  { label: "待退款", value: "2" },
  { label: "订单已支付", value: "3" },
  { label: "订单完成", value: "4" },
  { label: "订单关闭", value: "5" },
  { label: "订单取消", value: "6" },
  { label: "异常订单", value: "7" },
];
function cancel() {
  open.value = false;
  refundOpen.value = false;
  emit("update:modelValue", false);
}
// 退款
const refund = () => {
  refundOpen.value = true;
};
const submitForm = () => {
  proxy.$refs["tagRef"].validate((valid) => {
    if (valid) {
      refundForm.value.orderId = form.value.orderId;
      refundForm.value.actualFee = form.value.actualFee;
      applyRefund(refundForm.value).then((response) => {
        proxy.$modal.msgSuccess("退款成功");
        refundOpen.value = false;
      });
    }
  });
};
function refundCancel() {
  refundOpen.value = false;
  refundForm.value = {};
}
</script>
<style lang="scss" scoped></style>
