<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单验证测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
</head>
<body>
    <div id="app">
        <el-container>
            <el-main>
                <h1>共享方案表单验证测试</h1>
                
                <el-form ref="shareRef" :model="form" :rules="rules" label-width="120px" style="max-width: 600px;">
                    <el-form-item label="重复共享" prop="cycleType">
                        <el-select v-model="form.cycleType" placeholder="请选择重复共享">
                            <el-option label="今日" :value="1"></el-option>
                            <el-option label="每日" :value="2"></el-option>
                            <el-option label="周末" :value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    
                    <el-form-item label="是否跨天" prop="isCrossDay">
                        <el-radio-group v-model="form.isCrossDay" @change="onCrossDayChange">
                            <el-radio :value="0">当日</el-radio>
                            <el-radio :value="1">次日</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    
                    <el-form-item label="开始时间" prop="shareStartTime">
                        <el-time-picker
                            v-model="form.shareStartTime"
                            value-format="HH:mm:ss"
                            format="HH:mm"
                            placeholder="选择共享开始时间"
                            style="width: 100%"
                        />
                    </el-form-item>
                    
                    <el-form-item label="结束时间" prop="shareEndTime">
                        <el-time-picker
                            v-model="form.shareEndTime"
                            value-format="HH:mm:ss"
                            format="HH:mm"
                            placeholder="选择共享结束时间"
                            style="width: 100%"
                        />
                    </el-form-item>
                    
                    <el-form-item>
                        <el-button type="primary" @click="submitForm">验证表单</el-button>
                        <el-button @click="resetForm">重置表单</el-button>
                    </el-form-item>
                </el-form>
                
                <div style="margin-top: 20px;">
                    <h3>测试用例：</h3>
                    <el-button @click="testCase1" size="small">当日模式：9:00-17:00</el-button>
                    <el-button @click="testCase2" size="small">当日模式：17:00-9:00（错误）</el-button>
                    <el-button @click="testCase3" size="small">次日模式：22:00-6:00</el-button>
                    <el-button @click="testCase4" size="small">次日模式：9:00-17:00（错误）</el-button>
                </div>
            </el-main>
        </el-container>
    </div>

    <script>
        const { createApp, ref, reactive } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const shareRef = ref();
                const form = reactive({
                    cycleType: 1,
                    isCrossDay: 0,
                    shareStartTime: '',
                    shareEndTime: ''
                });

                const rules = reactive({
                    cycleType: [
                        { required: true, message: "重复共享不能为空", trigger: "change" },
                    ],
                    isCrossDay: [
                        { required: true, message: "是否跨天不能为空", trigger: "change" },
                    ],
                    shareStartTime: [
                        { required: true, message: "开始时间不能为空", trigger: "blur" },
                        {
                            validator: (rule, value, callback) => {
                                if (!value || !form.shareEndTime) {
                                    callback();
                                    return;
                                }
                                
                                // 创建日期对象用于时间比较
                                const baseDate = '2000-01-01';
                                const startTime = new Date(`${baseDate} ${value}`);
                                const endTime = new Date(`${baseDate} ${form.shareEndTime}`);
                                
                                // 根据是否跨天进行不同的验证
                                if (form.isCrossDay === 0) {
                                    // 当日：开始时间应早于结束时间
                                    if (startTime >= endTime) {
                                        callback(new Error("当日模式下，开始时间必须早于结束时间"));
                                    } else {
                                        callback();
                                    }
                                } else if (form.isCrossDay === 1) {
                                    // 次日：开始时间应晚于结束时间（跨天）
                                    if (startTime <= endTime) {
                                        callback(new Error("次日模式下，开始时间必须晚于结束时间"));
                                    } else {
                                        callback();
                                    }
                                } else {
                                    callback();
                                }
                            },
                            trigger: "blur",
                        },
                    ],
                    shareEndTime: [
                        { required: true, message: "结束时间不能为空", trigger: "blur" },
                        {
                            validator: (rule, value, callback) => {
                                if (!value || !form.shareStartTime) {
                                    callback();
                                    return;
                                }
                                
                                // 创建日期对象用于时间比较
                                const baseDate = '2000-01-01';
                                const startTime = new Date(`${baseDate} ${form.shareStartTime}`);
                                const endTime = new Date(`${baseDate} ${value}`);
                                
                                // 根据是否跨天进行不同的验证
                                if (form.isCrossDay === 0) {
                                    // 当日：开始时间应早于结束时间
                                    if (startTime >= endTime) {
                                        callback(new Error("当日模式下，开始时间必须早于结束时间"));
                                    } else {
                                        callback();
                                    }
                                } else if (form.isCrossDay === 1) {
                                    // 次日：开始时间应晚于结束时间（跨天）
                                    if (startTime <= endTime) {
                                        callback(new Error("次日模式下，开始时间必须晚于结束时间"));
                                    } else {
                                        callback();
                                    }
                                } else {
                                    callback();
                                }
                            },
                            trigger: "blur",
                        },
                    ],
                });

                const onCrossDayChange = () => {
                    // 当跨天选项改变时，重新验证开始时间和结束时间
                    if (form.shareStartTime) {
                        shareRef.value.validateField("shareStartTime");
                    }
                    if (form.shareEndTime) {
                        shareRef.value.validateField("shareEndTime");
                    }
                };

                const submitForm = () => {
                    shareRef.value.validate((valid) => {
                        if (valid) {
                            ElMessage.success('表单验证通过！');
                        } else {
                            ElMessage.error('表单验证失败！');
                        }
                    });
                };

                const resetForm = () => {
                    shareRef.value.resetFields();
                };

                // 测试用例
                const testCase1 = () => {
                    form.isCrossDay = 0;
                    form.shareStartTime = '09:00:00';
                    form.shareEndTime = '17:00:00';
                };

                const testCase2 = () => {
                    form.isCrossDay = 0;
                    form.shareStartTime = '17:00:00';
                    form.shareEndTime = '09:00:00';
                };

                const testCase3 = () => {
                    form.isCrossDay = 1;
                    form.shareStartTime = '22:00:00';
                    form.shareEndTime = '06:00:00';
                };

                const testCase4 = () => {
                    form.isCrossDay = 1;
                    form.shareStartTime = '09:00:00';
                    form.shareEndTime = '17:00:00';
                };

                return {
                    shareRef,
                    form,
                    rules,
                    onCrossDayChange,
                    submitForm,
                    resetForm,
                    testCase1,
                    testCase2,
                    testCase3,
                    testCase4
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
