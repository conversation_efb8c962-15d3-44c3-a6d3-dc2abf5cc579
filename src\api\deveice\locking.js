import request from '@/utils/request'

// 查询车位锁信息列表
export function listLocking(query) {
  return request({
    url: '/device/lock/getLockList',
    method: 'get',
    params: query
  })
}

// 查询车位锁信息详细
export function getLocking(query) {
  return request({
    url: '/device/lock/getLockInfo',
    method: 'get',
    params: query
  })
}

// 新增车位锁信息
export function addLocking(data) {
  return request({
    url: '/device/lock/addShareLock',
    method: 'post',
    data: data
  })
}

// 修改车位锁信息
export function updateLocking(data) {
  return request({
    url: '/device/lock/updateShareLock',
    method: 'put',
    data: data
  })
}

// 获取车位锁二维码
export function getLockQrCode(query) {
  return request({
    url: '/device/lock/qrcode',
    method: 'get',
    params: query
  })
}
// 开开锁
export function operateLock(data) {
  return request({
    url: '/device/lock/operateLock',
    method: 'post',
    data: data
  })
}
// 解绑车位锁
export function unbindLock(lockId) {
  return request({
    url: '/device/lock/releaseLock/' + lockId,
    method: 'get',

  })
}
//增加或者修改共享方案
export function addOrUpdateShare(data) {
  return request({
    url: '/device/lock/updateBill',
    method: 'post',
    data: data
  })
}
//通过车位id查询车锁共享详情
export function getShareInfo(shareId) {
  return request({
    url: '/device/lock/getLockShareBill/' + shareId,
    method: 'get',

  })
}
// 查询设备列表
export function listDevice(query) {
  return request({
    url: '/device/inventory/getInventories',
    method: 'get',
    params: query
  })
}
// 获取车场列表
export function getParkList(query) {
  return request({
    url: '/system/park/getParkList',
    method: 'get',
    params: query
  })
}