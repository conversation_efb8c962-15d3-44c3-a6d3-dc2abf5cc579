<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间验证逻辑测试</title>
</head>
<body>
    <h1>时间验证逻辑测试</h1>
    
    <div>
        <h2>测试用例</h2>
        <div id="test-results"></div>
    </div>

    <script>
        // 模拟验证函数
        function validateTime(startTime, endTime, isCrossDay) {
            if (!startTime || !endTime) {
                return { valid: true, message: "时间为空，跳过验证" };
            }
            
            // 创建日期对象用于时间比较
            const baseDate = '2000-01-01';
            const start = new Date(`${baseDate} ${startTime}`);
            const end = new Date(`${baseDate} ${endTime}`);
            
            if (isCrossDay === 0) {
                // 当日：开始时间应早于结束时间
                if (start >= end) {
                    return { valid: false, message: "当日模式下，开始时间必须早于结束时间" };
                } else {
                    return { valid: true, message: "当日模式验证通过" };
                }
            } else if (isCrossDay === 1) {
                // 次日：开始时间应晚于结束时间（跨天）
                if (start <= end) {
                    return { valid: false, message: "次日模式下，开始时间必须晚于结束时间" };
                } else {
                    return { valid: true, message: "次日模式验证通过" };
                }
            }
            
            return { valid: true, message: "未知模式" };
        }

        // 测试用例
        const testCases = [
            // 当日模式测试
            { startTime: "09:00:00", endTime: "17:00:00", isCrossDay: 0, expected: true, description: "当日模式：9点到17点" },
            { startTime: "17:00:00", endTime: "09:00:00", isCrossDay: 0, expected: false, description: "当日模式：17点到9点（错误）" },
            { startTime: "12:00:00", endTime: "12:00:00", isCrossDay: 0, expected: false, description: "当日模式：相同时间（错误）" },
            
            // 次日模式测试
            { startTime: "22:00:00", endTime: "06:00:00", isCrossDay: 1, expected: true, description: "次日模式：22点到次日6点" },
            { startTime: "09:00:00", endTime: "17:00:00", isCrossDay: 1, expected: false, description: "次日模式：9点到17点（错误）" },
            { startTime: "23:30:00", endTime: "05:30:00", isCrossDay: 1, expected: true, description: "次日模式：23:30到次日5:30" },
        ];

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            let html = '<table border="1" style="border-collapse: collapse; width: 100%;">';
            html += '<tr><th>描述</th><th>开始时间</th><th>结束时间</th><th>跨天模式</th><th>期望结果</th><th>实际结果</th><th>状态</th></tr>';
            
            testCases.forEach((testCase, index) => {
                const result = validateTime(testCase.startTime, testCase.endTime, testCase.isCrossDay);
                const passed = result.valid === testCase.expected;
                
                html += `<tr style="background-color: ${passed ? '#d4edda' : '#f8d7da'};">`;
                html += `<td>${testCase.description}</td>`;
                html += `<td>${testCase.startTime}</td>`;
                html += `<td>${testCase.endTime}</td>`;
                html += `<td>${testCase.isCrossDay === 0 ? '当日' : '次日'}</td>`;
                html += `<td>${testCase.expected ? '通过' : '失败'}</td>`;
                html += `<td>${result.valid ? '通过' : '失败'} - ${result.message}</td>`;
                html += `<td>${passed ? '✅ PASS' : '❌ FAIL'}</td>`;
                html += '</tr>';
            });
            
            html += '</table>';
            resultsDiv.innerHTML = html;
        }

        // 页面加载后运行测试
        window.onload = runTests;
    </script>
</body>
</html>
