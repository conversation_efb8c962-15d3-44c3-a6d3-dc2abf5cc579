<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="账号编号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入账号编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="状态" prop="paymentStatus">
        <el-select
          v-model="queryParams.paymentStatus"
          placeholder="请选择提现状态"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in payment_status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="申请时间" prop="dataRangeTime">
        <el-date-picker
          v-model="queryParams.dataRangeTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="To"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item label="用户类型" prop="userType">
        <el-select
          v-model="queryParams.userType"
          placeholder="请选择用户类型"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in user_type"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="logList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column label="账号" align="center" prop="phone" />
      <el-table-column label="姓名" align="center" prop="username" />
      <el-table-column label="类型" align="center" prop="userType">
        <template #default="scope">
          <dict-tag :value="scope.row.userType" :options="user_type" />
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="operateTime" />
      <el-table-column label="提现账户" align="center" prop="bankNum" />
      <el-table-column label="提现银行" align="center" prop="bank" />
      <el-table-column label="提现金额" align="center" prop="amount" />

      <el-table-column label="状态" align="center" prop="paymentStatus">
        <template #default="scope">
          <dict-tag
            :value="scope.row.paymentStatus"
            :options="payment_status"
          />
        </template>
      </el-table-column>
      <!-- 操作 -->
      <el-table-column label="操作" align="center" width="150">
        <template #default="scope">
          <el-button
            type="text"
            size="small"
            @click="handleUpdate(scope.row)"
             v-hasPermi="['finance:withdrawal:update']"
            v-if="scope.row.paymentStatus == '2'"
            >打款完成</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
  </div>
</template>

<script setup name="Notice">
import { listWithdraw, putMoney } from "@/api/finance/statements";
import { ElMessage, ElMessageBox } from "element-plus";
const { proxy } = getCurrentInstance();

const logList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    bankNum: undefined,
    paymentStatus: undefined,
    userType: undefined,
    dataRangeTime: undefined,
  },
  rules: {
    tagName: [{ required: true, message: "标签类型不能为空", trigger: "blur" }],
    orderBy: [{ required: true, message: "排序不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  if (queryParams.value.dataRangeTime) {
    queryParams.value.startApplyTime = queryParams.value.dataRangeTime[0];
    queryParams.value.endApplyTime = queryParams.value.dataRangeTime[1];
  } else {
    queryParams.value.startApplyTime = undefined;
    queryParams.value.endApplyTime = undefined;
  }
  listWithdraw(queryParams.value).then((response) => {
    logList.value = response.rows;
    total.value = parseInt(response.total);
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    status: "0",
  };
  proxy.resetForm("tagRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加标签";
}

/**修改按钮操作 */
function handleUpdate(row) {
  ElMessageBox.confirm(
    "请核对打款账户是否正，且打款已完成，更改后状态不可更变。",
    "提示",
    {
      type: "warning",
    }
  )
    .then(() => {
      putMoney({
        withdrawalId: row.withdrawalId,
        paymentStatus: "1",
      }).then((response) => {
        proxy.$modal.msgSuccess("打款成功");
        getList();
      });
    })
    .catch(() => {});
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["tagRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != undefined) {
        updateTag(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTag(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const tagIds = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除标签编号为"' + tagIds + '"的数据项？')
    .then(function () {
      return delTag(tagIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

getList();
// 静态数据
//操作类型 value = "操作类型", allowableValues = "1, 2, 3"
const user_type = [
  { label: "用户", value: "1" },
  { label: "代理商", value: "2" },
];
//设备状态 value = "设备状态", allowableValues = "0, 1, 2"
const payment_status = [
  { label: "已打款", value: "1" },
  { label: "待打款", value: "2" },
];
</script>
