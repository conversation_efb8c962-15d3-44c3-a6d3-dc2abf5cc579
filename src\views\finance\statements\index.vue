<!-- 
 * @description: k库存管理
 * @fileName: index.vue 
 * @author: 李文滔 
 * @date: 2025-04-02 21:18:57 
 * @path:  
 * @version: V1.0.0 
!-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入订单号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车场" prop="parkName">
        <el-input
          v-model="queryParams.parkName"
          placeholder="请输入车场名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="下单时间" prop="dataRangeTime">
        <el-date-picker
          v-model="queryParams.dataRangeTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="To"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择订单状态"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in order_state"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8"   v-hasPermi="['device:order:query']">
      <el-col :span="3">
        <el-card class="dis-play">
          <el-statistic title="实收金额" :value="inventoryData.totalActualFee">
            <template #suffix>元</template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="3">
        <el-card>
          <el-statistic
            title="账户支出"
            :value="inventoryData.totalManualRefund"
          >
            <template #suffix>元</template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="3">
        <el-card>
          <el-statistic
            title="账户收入"
            :value="inventoryData.totalPropertyFee"
          >
            <template #suffix>元</template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="3">
        <el-card>
          <el-statistic title="净利润" :value="inventoryData.totalActualIncome">
            <template #suffix>元</template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="12" class="right-grid">
        <el-button type="primary" plain icon="Plus" @click="handleExport"
        v-hasPermi="['finance:report:export']"
          >导出</el-button
        >
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tagList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column
        label="订单号"
        align="center"
        prop="orderNo"
        width="300px"
      />

      <el-table-column label="订单类型" align="center" prop="orderType">
        <template #default="scope">
          <dict-tag :value="scope.row.orderType" :options="order_type" />
        </template>
      </el-table-column>
      <el-table-column
        label="用户手机号"
        align="center"
        prop="phone"
        width="200px"
      />
      <el-table-column
        label="车牌号"
        align="center"
        prop="plateNo"
        width="200px"
      />
      <el-table-column
        label="车场"
        align="center"
        prop="parkName"
        width="200px"
      />

      <el-table-column
        label="下单时间"
        align="center"
        prop="orderTime"
        width="200px"
      >
        <template #default="scope">
          {{ parseTime(scope.row.orderTime) }}
        </template>
      </el-table-column>
      <el-table-column
        label="结束时间"
        align="center"
        prop="finishTime"
        width="200px"
      >
        <template #default="scope">
          {{ parseTime(scope.row.finishTime) }}
        </template>
      </el-table-column>

      <el-table-column label="正常消费" align="center" prop="parkAmount">
        <template #default="scope">
          {{ scope.row.parkAmount || 0 }} 元
        </template>
      </el-table-column>

      <el-table-column label="额外费用" align="center" prop="timeoutAmount">
        <template #default="scope">
          {{ scope.row.timeoutAmount || 0 }} 元
        </template>
      </el-table-column>
      <el-table-column label="应收金额" align="center" prop="payableAmount">
        <template #default="scope"> {{ scope.row.payableAmount || 0 }}元 </template>
      </el-table-column>
      <el-table-column label="实收金额" align="center" prop="actualFee">
        <template #default="scope">
          {{ scope.row.actualFee || 0 }} 元
        </template>
      </el-table-column>
      <el-table-column
        label="需退费金额"
        align="center"
        prop="needRefund"
        width="100px"
      >
        <template #default="scope">
          {{ scope.row.needRefund || 0 }} 元
        </template>
      </el-table-column>
      <el-table-column
        label="待补缴金额"
        align="center"
        prop="conscienceMoney"
        width="100px"
      >
        <template #default="scope">
          {{ scope.row.conscienceMoney || 0 }} 元
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="200">
        <template #default="scope">
          <dict-tag :value="scope.row.status" :options="order_state" />
        </template>
      </el-table-column>
      <el-table-column label="人工退费" align="center" prop="manualRefund">
        <template #default="scope">
          {{ scope.row.manualRefund || 0 }} 元
        </template>
      </el-table-column>
      <el-table-column label="分成扣除" align="center" prop="propertyFee">
        <template #default="scope">
          {{ scope.row.propertyFee || 0 }} 元
        </template>
      </el-table-column>

      <el-table-column label="实际收入" align="center" prop="actualIncome">
        <template #default="scope">
          {{ scope.row.actualIncome || 0 }} 元
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="200"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Tickets"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['finance:report:view']"
            >订单详情</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['finance:report:refund']"
            >退款</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改库存对话框 -->
    <oderDetails v-model="open" :item="orderItem"></oderDetails>
  </div>
</template>

<script setup name="Notice">
import { listStatements, getStatements } from "@/api/finance/statements";
import oderDetails from "../../deveice/order/components/oderDetails.vue";
const { proxy } = getCurrentInstance();

const tagList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const orderItem = ref({});
const inventoryData = ref({
  bindingCount: 0,
  unbindingCount: 0,
  totalInventories: 0,
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNo: undefined,
    phone: undefined,
    parkName: undefined,
    plateNo: undefined,
    status: undefined,
    dataRangeTime: [],
  },
  rules: {
    lockNo: [
      { required: true, message: "设备编号不能为空", trigger: "blur" },
    ],
    phone: [{ required: true, message: "手机号不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  if (queryParams.value.dataRangeTime?.length > 0) {
    queryParams.value.orderBeginTime = queryParams.value.dataRangeTime[0];
    queryParams.value.orderEndTime = queryParams.value.dataRangeTime[1];
  } else {
    queryParams.value.orderBeginTime = undefined;
    queryParams.value.orderEndTime = undefined;
  }
  listStatements(queryParams.value).then((response) => {
    tagList.value = response.rows;
    total.value = parseInt(response.total);
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    status: "0",
  };
  proxy.resetForm("tagRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();
  open.value = true;

  orderItem.value = row;
}

// 导出
function handleExport() {
  proxy.download(
    "finance/export",
    {
      ...queryParams.value,
    },
    `财务信息.xlsx`
  );
}

// 获取统计数据
function getStatistics() {
  getStatements().then((response) => {
    inventoryData.value = response.data;
  });
}
getList();
getStatistics();
// 静态数据
const order_type = [
  { value: "1", label: "普通计费" },
  { value: "2", label: "特殊计费" },
];
// 预约订单状态 1:待交押金 0：已交押金待入场 3：已入场 1：已完成 2：已取消(主动) 4：已取消(自动取消) 5:待补缴
const order_state = [
  { label: "进行中", value: "0" },
  { label: "已部分支付(待补缴)", value: "1" },
  { label: "待退款", value: "2" },
  { label: "订单已支付", value: "3" },
  { label: "订单完成", value: "4" },
  { label: "订单关闭", value: "5" },
  { label: "订单取消", value: "6" },
  { label: "异常订单", value: "7" },
];
</script>
<style lang="scss" scoped>
.right-grid {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}
</style>
