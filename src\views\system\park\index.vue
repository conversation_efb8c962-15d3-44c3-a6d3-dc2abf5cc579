<template>
  <div class="app-container">
    <el-row>
      <splitpanes class="default-theme">
        <pane size="16">
          <el-col>
            <div class="head-container">
              <el-tree
                :data="areaOptions"
                :props="{ label: 'areaName', children: 'children' }"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                ref="deptTreeRef"
                node-key="areaId"
                highlight-current
                default-expand-all
                @node-click="handleNodeClick"
              />
            </div>
          </el-col>
        </pane>
        <pane size="84">
          <el-col>
            <el-form
              :model="queryParams"
              ref="queryRef"
              :inline="true"
              v-show="showSearch"
              label-width="80px"
            >
              <el-form-item label="车场名称" prop="parkName">
                <el-input
                  v-model="queryParams.parkName"
                  placeholder="请输入停车场名称"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>

              <el-form-item label="手机号" prop="parkPhone">
                <el-input
                  v-model="queryParams.parkPhone"
                  placeholder="请输入手机号"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="类型" prop="parkType">
                <el-select
                  v-model="queryParams.parkType"
                  placeholder="请选择车场类型"
                  clearable
                  @keyup.enter="handleQuery"
                  style="width: 200px"
                >
                  <el-option
                    v-for="item in parkTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery"
                  >搜索</el-button
                >
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  plain
                  icon="Plus"
                  @click="handleAdd"
                  v-hasPermi="['system:park:add']"
                  >新增</el-button
                >
              </el-col>

              <right-toolbar
                v-model:showSearch="showSearch"
                @queryTable="getList"
              ></right-toolbar>
            </el-row>

            <el-table
              v-loading="loading"
              :data="parkList"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" align="center" type="index" />
              <el-table-column
                label="车场名称"
                align="center"
                prop="parkName"
                width="200px"
              />
              <el-table-column
                label="车场地址"
                align="center"
                prop="parkAddress"
                width="200px"
              />
              <el-table-column
                label="手机号"
                align="center"
                prop="parkPhone"
                width="200px"
              />

              <el-table-column
                label="类型"
                align="center"
                prop="parkType"
                width="100px"
              >
                <template #default="{ row }">
                  <dict-tag
                    :value="row.parkType"
                    :options="parkTypeOptionsSub"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="入场免费时间"
                align="center"
                prop="freeInTime"
                width="200px"
              />
              <el-table-column
                label="出场免费时间"
                align="center"
                prop="freeOutTime"
                width="200px"
              />
              <el-table-column label="V1状态" align="center" prop="state">
                <template #default="{ row }">
                  <el-tag v-if="row.state == -1" type="success" size="mini"
                    >不在线</el-tag
                  >
                  <el-tag v-else-if="row.state == 1" type="danger" size="mini"
                    >在线</el-tag
                  >
                </template>
              </el-table-column>
              <el-table-column label="单价" align="center" prop="unitCharge" />
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
                fixed="right"
                width="300"
              >
                <template #default="scope">
                  <el-button
                    link
                    type="primary"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['system:park:update']"
                    >编辑</el-button
                  >
                  <el-button
                    link
                    type="primary"
                    @click="handleLink(scope.row)"
                    v-hasPermi="['system:park:user']"
                    >人员管理</el-button
                  >
                  <el-button
                    link
                    type="primary"
                    @click="handleAdminLink(scope.row)"
                    v-hasPermi="['system:park:admin']"
                    >管理员配置</el-button
                  >

                  <el-dropdown>
                    <el-button link type="primary">更多</el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item>
                          <el-button
                            link
                            type="primary"
                            @click="handleDevice(scope.row)"
                            v-hasPermi="['system:park:device:list']"
                            >设备状态</el-button
                          ></el-dropdown-item
                        >
                        <el-dropdown-item>
                          <el-button
                            link
                            type="primary"
                            @click="handleRecord(scope.row)"
                            v-hasPermi="['system:park:record']"
                            >进出记录</el-button
                          ></el-dropdown-item
                        >
                        <el-dropdown-item>
                          <el-button
                            link
                            type="primary"
                            @click="handleDelete(scope.row)"
                            v-hasPermi="['system:park:remove']"
                            >删除</el-button
                          ></el-dropdown-item
                        >
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或修改车场对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="parkRef" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="车场类型" prop="parkType">
          <el-select v-model="form.parkType" placeholder="请选择车场类型">
            <el-option
              v-for="item in parkTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-row>
          <el-col :span="24">
            <el-form-item label="车场名称" prop="parkName">
              <el-input
                v-model="form.parkName"
                placeholder="请输入车场名称"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="车场地址" prop="parkAddress">
          <el-input
            v-model="form.parkAddress"
            type="textarea"
            rows="5"
            placeholder="请输入停车场详细地址"
          />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号" prop="parkPhone">
              <el-input
                v-model="form.parkPhone"
                placeholder="请输入手机号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地区" prop="areaId">
              <el-tree-select
                v-model="form.areaId"
                :data="enabledAreaOptions"
                :props="{
                  value: 'areaId',
                  label: 'areaName',
                  children: 'children',
                }"
                value-key="areaId"
                placeholder="请选择归属部门"
                check-strictly
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input v-model="form.longitude" placeholder="请输入经度" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input v-model="form.latitude" placeholder="请输入纬度" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="入场免费时间" prop="freeInTime">
              <el-input
                v-model="form.freeInTime"
                type="number"
                placeholder="请输入入场免费时间"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出场免费时间" prop="freeOutTime">
              <el-input
                v-model="form.freeOutTime"
                type="number"
                placeholder="请输入出场免费时间"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="单价" prop="unitCharge">
              <el-input v-model="form.unitCharge" placeholder="请输入单价" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单价单位" prop="timeUnit">
              <el-select v-model="form.timeUnit" placeholder="请选择单价单位">
                <el-option
                  v-for="item in time_init"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="车场备注" prop="remark">
              <el-input
                type="textarea"
                :rows="5"
                v-model="form.remark"
                placeholder="请输入车场备注"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Park">
import {
  listPark,
  getPark,
  delPark,
  addPark,
  updatePark,
  getParkByArea,
} from "@/api/system/park";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
const { proxy } = getCurrentInstance();
const router = useRouter();
const parkList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
// 区域树
const areaOptions = ref(undefined);
const enabledAreaOptions = ref(undefined);
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    parkName: null,
    parkAddress: null,
    parkPhone: null,
    status: null,
    longitude: null,
    latitude: null,
    menuNumber: null,
    parkType: null,
    remark: null,
  },
  rules: {
    parkType: [
      { required: true, message: "请选择车场类型", trigger: "change" },
    ],
    parkName: [
      { required: true, message: "请输入车场名称", trigger: "blur" },
    ],
    longitude: [{ required: true, message: "请输入经度", trigger: "blur" }],
    latitude: [{ required: true, message: "请输入纬度", trigger: "blur" }],
    parkPhone: [
      { required: true, message: "请输入手机号", trigger: "blur" },
      {
        pattern: /^1[3456789]\d{9}$/,
        message: "请输入正确的手机号",
        trigger: "blur",
      },
    ],
    parkAddress: [
      { required: true, message: "请输入车场地址", trigger: "blur" },
    ],
    areaId: [{ required: true, message: "请选择地区", trigger: "change" }],
    freeInTime: [
      { required: true, message: "请输入入场免费时间", trigger: "blur" },
      {
        pattern: /^[0-9]*[1-9][0-9]*$/,
        message: "请输入正整数",
        trigger: "blur",
      },
    ],
    freeOutTime: [
      { required: true, message: "请输入出场免费时间", trigger: "blur" },
      {
        pattern: /^[0-9]*[1-9][0-9]*$/,
        message: "请输入正整数",
        trigger: "blur",
      },
    ],
    unitCharge: [
      { required: true, message: "请输入单价", trigger: "blur" },
      {
        pattern: /^[0-9]+(.[0-9]{1,2})?$/,
        message: "请输入正确的金额",
        trigger: "blur",
      },
    ],
    timeUnit: [
      { required: true, message: "请选择单价单位", trigger: "change" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询车场列表 */
function getList() {
  loading.value = true;
  listPark(queryParams.value).then((response) => {
    parkList.value = response.rows;
    total.value = parseInt(response.total);
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    parkId: null,
    parkName: null,
    parkAddress: null,
    parkPhone: null,
    status: null,
    longitude: null,
    latitude: null,
    menuNumber: null,
    parkType: null,
    remark: null,
    createdTime: null,
    updatedTime: null,
    illegalMultiple: 4,
  };
  proxy.resetForm("parkRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加车场";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.parkId || ids.value;
  getPark(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改车场";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["parkRef"].validate((valid) => {
    if (valid) {
      if (form.value.parkId != null) {
        updatePark(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addPark(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除车场编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delPark(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 查询部门下拉树结构 */
function getAreaTree() {
  getParkByArea().then((response) => {
    areaOptions.value = proxy.handleTree(response.data, "areaId");
    enabledAreaOptions.value = filterDisabledDept(
      JSON.parse(JSON.stringify(areaOptions.value))
    );
  });
}
/** 过滤禁用的部门 */
function filterDisabledDept(deptList) {
  return deptList.filter((dept) => {
    if (dept.disabled) {
      return false;
    }
    if (dept.children && dept.children.length) {
      dept.children = filterDisabledDept(dept.children);
    }
    return true;
  });
}
/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};
// 跳转到人员管理
function handleLink(row) {
  router.push({
    name: "StaffConfig",
    query: { parkId: row.parkId },
  });
}
// 跳转到管理员配置
function handleAdminLink(row) {
  router.push({
    name: "AdminStaffConfig",
    query: { parkId: row.parkId },
  });
}
// 查询车场得进出记录
function handleRecord(row) {
  router.push({
    name: "ParkRecord",
    query: { parkNo: row.parkId },
  });
}
// 查询车场得设备状态
function handleDevice(row) {
  router.push({
    name: "DeviceRecord",
    query: { parkNo: row.parkNo },
  });
}
// 查询车场
function handleNodeClick(data) {
  queryParams.value.areaId = data.areaId;

  handleQuery();
}

getList();
getAreaTree();
// 静态数据
const parkTypeOptions = [
  { label: "开放式车场", value: 0 },
  { label: "封闭式车场", value: 1 },
  { label: "路边式车场", value: 2 },
];
const parkTypeOptionsSub = [
  { label: "开放式车场", value: '0' },
  { label: "封闭式车场", value: '1' },
  { label: "路边式车场", value: '2' },
];
const time_init = [
  { label: "分钟", value: 1 },
  { label: "小时", value: 2 },
];
</script>
