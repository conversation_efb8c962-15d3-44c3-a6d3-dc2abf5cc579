<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="类型" prop="feedbackType">
        <el-select
          v-model="queryParams.feedbackType"
          placeholder="请选择类型"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in feedback_type"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间" prop="dataRangeTime">
        <el-date-picker
          v-model="queryParams.dataRangeTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="To"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="logList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column label="时间" align="center" prop="feedbackTime">
        <template #default="scope">
          {{ parseTime(scope.row.feedbackTime) }}
        </template>
      </el-table-column>
      <el-table-column label="账号" align="center" prop="weChatNickName" />

      <el-table-column label="类型" align="center" prop="feedbackType">
        <template #default="scope">
          <dict-tag :value="scope.row.feedbackType" :options="feedback_type" />
        </template>
      </el-table-column>

      <el-table-column label="描述" align="center" prop="remark" />
      <el-table-column label="联系方式" align="center" prop="phone" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Notice">
import { listFeedBack } from "@/api/marketing/feedBack";
import { parseTime } from "../../../utils/ruoyi";

const { proxy } = getCurrentInstance();

const logList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    feedbackType: undefined,
    dataRangeTime: undefined,
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  if (queryParams.value.dataRangeTime) {
    queryParams.value.feedbackStartTime = queryParams.value.dataRangeTime[0];
    queryParams.value.feedbackEndTime = queryParams.value.dataRangeTime[1];
  } else {
    queryParams.value.feedbackStartTime = undefined;
    queryParams.value.feedbackEndTime = undefined;
  }
  listFeedBack(queryParams.value).then((response) => {
    logList.value = response.rows;
    total.value = parseInt(response.total);
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    status: "0",
  };
  proxy.resetForm("tagRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加标签";
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();
  const tagId = row.id || ids.value;
  getTag(tagId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改标签";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["tagRef"].validate((valid) => {
    if (valid) {
      if (form.value.parkId != undefined) {
        updateTag(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTag(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const tagIds = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除标签编号为"' + tagIds + '"的数据项？')
    .then(function () {
      return delTag(tagIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

getList();
// 静态数据
// @ApiModelProperty("反馈类型：1：扣款异常，2：页面加载失败，3：保证金未返回，4：升开锁异常，5：其他")
const feedback_type = [
  { label: "扣款异常", value: "1" },
  { label: "开关锁异常", value: "2" },
  { label: "押金未退还", value: "3" },
  { label: "页面加载失败", value: "4" },
  { label: "其他", value: "5" },
];
//设备状态 value = "设备状态", allowableValues = "0, 1, 2"
</script>
